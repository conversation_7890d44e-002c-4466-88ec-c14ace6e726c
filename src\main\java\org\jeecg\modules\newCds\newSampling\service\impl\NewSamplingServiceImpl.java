package org.jeecg.modules.newCds.newSampling.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.newCds.cdsUtils.CdsUtils;
import org.jeecg.modules.newCds.newSampling.child.entity.LianTask;
import org.jeecg.modules.newCds.newSampling.child.entity.Popular;
import org.jeecg.modules.newCds.newSampling.child.entity.ShiTask;
import org.jeecg.modules.newCds.newSampling.child.entity.TuanTask;
import org.jeecg.modules.newCds.newSampling.child.mapper.LianTaskMapper;
import org.jeecg.modules.newCds.newSampling.child.mapper.ShiTaskMapper;
import org.jeecg.modules.newCds.newSampling.child.mapper.TuanTaskMapper;
import org.jeecg.modules.newCds.newSampling.child.service.IPopularService;
import org.jeecg.modules.newCds.newSampling.child.service.IShiTaskService;
import org.jeecg.modules.newCds.newSampling.child.service.ITuanTaskService;
import org.jeecg.modules.newCds.newSampling.child.service.ILianTaskService;
import org.jeecg.modules.newCds.newSampling.service.ISamplingTaskReportService;
import org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport;
import org.jeecg.modules.newCds.newSampling.entity.dto.SampleDto;
import org.jeecg.modules.newCds.newSampling.entity.pojo.NewSampling;
import org.jeecg.modules.newCds.newSampling.entity.vo.ZoneVo;
import org.jeecg.modules.newCds.newSampling.entity.vo.SamplingReportVO;
import org.jeecg.modules.newCds.newSampling.mapper.NewSamplingMapper;
import org.jeecg.modules.newCds.newSampling.service.INewSamplingService;
import org.jeecg.modules.newCds.newSampling.utils.ProgressCalculator;
import org.jeecg.modules.newCds.newSampling.service.TaskStatusService;
import org.jeecg.modules.newCds.newSampling.enums.TaskStatusEnum;
import org.jeecg.modules.newCds.newSampling.service.SamplingResultPushService;
import org.jeecg.modules.system.mapper.SysZoneCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * @Description: 慢病抽样任务
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
@Service
@Slf4j
public class NewSamplingServiceImpl extends ServiceImpl<NewSamplingMapper, NewSampling> implements INewSamplingService {
    @Autowired
    private NewSamplingMapper samplingMapper;
    @Autowired
    private ShiTaskMapper shiMapper;
    @Autowired
    private TuanTaskMapper tuanMapper;
    @Autowired
    private LianTaskMapper lianMapper;
    @Autowired
    private ProgressCalculator progressCalculator;
    @Autowired
    private IPopularService popularService;
    @Autowired
    private ISamplingTaskReportService samplingTaskReportService;

    @Autowired
    private SysZoneCodeMapper sysZoneCodeMapper;

    @Autowired
    private TaskStatusService taskStatusService;

    @Autowired
    private IShiTaskService shiService;

    @Autowired
    private ITuanTaskService tuanService;

    @Autowired
    private ILianTaskService lianService;

    @Autowired
    private SamplingResultPushService pushService;
    //地区抽样分页
/*    @Override
    public List<ZoneVo> queryShi(String removeIds,String isStart,String sampleNum,String sampleRandom,String layerNum) {
        List<ZoneVo> list = samplingMapper.queryShi(removeIds,layerNum);
        //开始抽样
        if("true".equals(isStart)){
            //哈希随机数计算公式
            long seed = Integer.parseInt(sampleRandom) * 31L + list.hashCode();
            Random random = new Random(seed);
            //抽取指定数量
            List<ZoneVo> newList = random.ints(0, list.size()).distinct().limit(Long.parseLong(sampleNum)).mapToObj(list::get).collect(Collectors.toList());
            return newList;
        }
        return list;
    }*/

    //地区上报人数分页
    @Override
    public List<Popular> queryZoneList(SampleDto dto) {
        log.info("开始查询地区上报人数 - 任务ID: " + dto.getBtId() + ", 年份: " + dto.getYears() +
                ", 随机因子: " + dto.getSampleRandom() + ", 抽样数量: " + dto.getSampleNum());

        try {
            // 构建查询条件
            QueryWrapper<SamplingTaskReport> queryWrapper = buildQueryWrapper(dto);

            // 查询数据
            List<SamplingTaskReport> reportList = samplingTaskReportService.list(queryWrapper);
            log.info("数据查询完成 - 任务ID: " + dto.getBtId() + ", 查询到" +
                    (reportList != null ? reportList.size() : 0) + "条记录");

            // 处理空数据情况
            if (reportList == null || reportList.isEmpty()) {
                handleEmptyDataForDebug(dto);
                return new ArrayList<>();
            }

            // 转换为Popular格式
            List<Popular> popularList = convertToPopularList(reportList);
            log.info("数据转换完成 - 转换后数据条数: " + popularList.size());

            // 执行抽样（如果需要）
            List<Popular> result = performSamplingIfNeeded(popularList, dto);

            log.info("地区上报人数查询完成 - 任务ID: " + dto.getBtId() + ", 返回" + result.size() + "条数据");
            return result;

        } catch (Exception e) {
            log.error("查询地区上报人数失败 - 任务ID: {}, 错误: {}", dto.getBtId(), e.getMessage(), e);
            throw new RuntimeException("查询地区上报人数失败: " + e.getMessage());
        }
    }

    //地区上报数据分页 - 新方法，返回专门的VO
    @Override
    public List<SamplingReportVO> queryZoneReportList(SampleDto dto) {
        log.info("查询地区上报数据，参数: btId=" + dto.getBtId() + ", years=" + dto.getYears() + ", sampleRandom=" + dto.getSampleRandom() + ", sampleNum=" + dto.getSampleNum());

        // 构建查询条件
        QueryWrapper<SamplingTaskReport> queryWrapper = new QueryWrapper<>();

        // 根据任务ID查询
        if (dto.getBtId() != null && !dto.getBtId().isEmpty()) {
            queryWrapper.eq("task_id", dto.getBtId());
            log.info("查询条件: task_id = " + dto.getBtId());
        }

        // 根据年份查询
        if (dto.getYears() != null && !dto.getYears().isEmpty()) {
            queryWrapper.eq("years", dto.getYears());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");

        // 查询数据
        List<SamplingTaskReport> reportList = samplingTaskReportService.list(queryWrapper);
        log.info("查询到 " + (reportList != null ? reportList.size() : 0) + " 条上报数据");

        // 如果指定了sampleNum，则限制返回的数据条数
        if (dto.getSampleNum() != null && !dto.getSampleNum().isEmpty()) {
            try {
                int sampleNum = Integer.parseInt(dto.getSampleNum());
                if (sampleNum > 0 && reportList != null && reportList.size() > sampleNum) {
                    reportList = reportList.subList(0, sampleNum);
                    log.info("根据sampleNum=" + sampleNum + "限制返回数据条数: " + reportList.size());
                }
            } catch (NumberFormatException e) {
                log.warn("sampleNum参数格式错误: " + dto.getSampleNum());
            }
        }

        // 转换为VO对象
        List<SamplingReportVO> voList = new ArrayList<>();
        if (reportList != null && !reportList.isEmpty()) {
            for (SamplingTaskReport report : reportList) {
                SamplingReportVO vo = new SamplingReportVO();
                vo.setId(report.getId());
                vo.setTaskId(report.getTaskId());

                // 根据编码查询真实的地区名称
                String realZoneName = getZoneNameByCode(report.getZoneCode());
                String realStreetName = getZoneNameByCode(report.getStreetCode());

                log.info("地区名称转换 - 地区编码: {}, 原名称: {}, 转换后名称: {}",
                    report.getZoneCode(), report.getZoneName(), realZoneName);
                log.info("街道名称转换 - 街道编码: {}, 原名称: {}, 转换后名称: {}",
                    report.getStreetCode(), report.getStreetName(), realStreetName);

                vo.setZoneName(realZoneName != null ? realZoneName : report.getZoneName()); // 地区名称
                vo.setZoneCode(report.getZoneCode()); // 地区编码
                vo.setStreetName(realStreetName != null ? realStreetName : report.getStreetName()); // 街道名称
                vo.setStreetCode(report.getStreetCode()); // 街道编码
                // 转换String到Integer
                try {
                    if (report.getResidentPopulation() != null && !report.getResidentPopulation().isEmpty()) {
                        vo.setResidentPopulation(Integer.valueOf(report.getResidentPopulation()));
                    } else {
                        vo.setResidentPopulation(0);
                    }
                } catch (NumberFormatException e) {
                    vo.setResidentPopulation(0);
                }
                vo.setRemark(report.getRemark()); // 备注
                vo.setParentZoneName(report.getParentZoneName()); // 上级地区名称
                vo.setMonitorPoint(report.getMonitorPoint()); // 监测点
                vo.setReportStatus(report.getReportStatus()); // 上报状态
                vo.setReportStatusText("已上报"); // 上报状态文本
                vo.setYears(report.getYears()); // 年份
                vo.setCreateTime(report.getCreateTime()); // 创建时间
                vo.setCreateBy(report.getCreateBy()); // 创建人
                voList.add(vo);
            }
        }

        log.info("转换后VO数据条数: " + voList.size());
        return voList;
    }

    /**
     * 根据地区编码查询地区名称
     * @param zoneCode 地区编码
     * @return 地区名称
     */
    private String getZoneNameByCode(String zoneCode) {
        if (zoneCode == null || zoneCode.isEmpty()) {
            return null;
        }

        try {
            // 使用SysZoneCodeMapper查询地区名称
            String zoneName = sysZoneCodeMapper.queryCnameByCode(zoneCode);
            if (zoneName != null && !zoneName.isEmpty()) {
                return zoneName;
            } else {
                // 如果查询不到，返回null让前端显示编码
                return null;
            }
        } catch (Exception e) {
            log.error("查询地区名称失败，编码：" + zoneCode + ", 错误：" + e.getMessage());
            return null;
        }
    }

    //兵团确认抽样
    @Override
    @Transactional
    public String btSampleShi(SampleDto dto) {
        log.info("开始兵团级抽样确认 - 任务ID: {}, 地区IDs: {}, 师级抽样数量: {}",
                dto.getBtId(), dto.getZoneIds(), dto.getShiSampleNum());

        try {
            validateBtSampleParams(dto);

            List<ZoneVo> voList = getZoneListByIds(dto.getZoneIds());
            log.info("获取到{}个地区信息", voList.size());

            List<ShiTask> shiTaskList = createShiTaskList(voList, dto);

            if (!shiTaskList.isEmpty()) {
                shiService.saveBatch(shiTaskList);
                log.info("成功创建{}个师级任务", shiTaskList.size());
            }

            progressCalculator.updateAllRelatedProgress(dto.getBtId(), "main");
            log.info("主表进度更新完成");

            taskStatusService.updateMainTaskStatus(dto.getBtId(), TaskStatusEnum.WAITING_SHI_REPORT);
            log.info("主任务状态更新为待师级上报");

            pushSamplingResultsSafely(dto.getBtId(), "2");

            log.info("兵团级抽样确认完成 - 任务ID: {}", dto.getBtId());
            return "操作成功";

        } catch (Exception e) {
            log.error("兵团级抽样确认失败 - 任务ID: {}, 错误: {}", dto.getBtId(), e.getMessage(), e);
            throw e;
        }
    }
    //师级确认抽样
    @Override
    @Transactional
    public String shiSampleTuan(SampleDto dto) {
        log.info("开始师级抽样确认 - 师级ID: {}, 地区IDs: {}, 团级抽样数量: {}",
                dto.getShiId(), dto.getZoneIds(), dto.getTuanSampleNum());

        try {
            validateShiSampleParams(dto);

            List<TuanTask> tuanTaskList = createTuanTaskList(dto);
            log.info("创建了" + tuanTaskList.size() + "个团级任务");

            if (!tuanTaskList.isEmpty()) {
                tuanService.saveBatch(tuanTaskList);
                log.info("成功保存" + tuanTaskList.size() + "个团级任务");
            }

            taskStatusService.updateShiTaskStatus(dto.getShiId(), TaskStatusEnum.WAITING_TUAN_REPORT);
            log.info("师级任务状态更新为待团级上报");

            updateTaskProgress(dto.getShiId());
            log.info("师级任务进度更新完成");

            log.info("师级抽样确认完成 - 师级ID: {}", dto.getShiId());
            return "师级抽样确认成功";

        } catch (Exception e) {
            log.error("师级抽样确认失败 - 师级ID: " + dto.getShiId() + ", 错误: " + e.getMessage(), e);
            throw e;
        }
    }

    //团级确认抽样
    @Override
    @Transactional
    public String tuanSampleLian(SampleDto dto) {
        log.info("开始团级抽样确认 - 团级ID: {}, 地区IDs: {}, 连级抽样数量: {}",
                dto.getTuanId(), dto.getZoneIds(), dto.getLianSampleNum());

        try {
            validateTuanSampleParams(dto);

            List<LianTask> lianTaskList = createLianTaskList(dto);
            log.info("创建了" + lianTaskList.size() + "个连级任务");

            if (!lianTaskList.isEmpty()) {
                lianService.saveBatch(lianTaskList);
                log.info("成功保存" + lianTaskList.size() + "个连级任务");
            }

            taskStatusService.updateTuanTaskStatus(dto.getTuanId(), TaskStatusEnum.WAITING_LIAN_REPORT);
            log.info("团级任务状态更新为待连级上报");

            updateTaskProgress(dto.getTuanId());
            log.info("团级任务进度更新完成");

            log.info("团级抽样确认完成 - 团级ID: {}", dto.getTuanId());
            return "团级抽样确认成功";

        } catch (Exception e) {
            log.error("团级抽样确认失败 - 团级ID: " + dto.getTuanId() + ", 错误: " + e.getMessage(), e);
            throw e;
        }
    }

    //删除所有抽样任务
    @Override
    @Transactional
    public void removeAllTask(String id) {
        samplingMapper.deleteById(id);
        shiMapper.deleteByMainId(id);
        tuanMapper.deleteByBtId(id);
        lianMapper.deleteByBtId(id);
    }

    //更新任务进度
    @Override
    public void updateTaskProgress(String taskId) {
        try {
            String progress = calculateProgress(taskId);

            // 判断任务类型并更新对应的进度
            NewSampling mainTask = samplingMapper.selectById(taskId);
            if (mainTask != null) {
                // 主任务进度更新
                mainTask.setProgress(progress);
                samplingMapper.updateById(mainTask);
            } else {
                // 检查是否为师级任务
                ShiTask shiTask = shiMapper.selectById(taskId);
                if (shiTask != null) {
                    shiTask.setProgress(progress);
                    shiMapper.updateById(shiTask);
                } else {
                    // 检查是否为团级任务
                    TuanTask tuanTask = tuanMapper.selectById(taskId);
                    if (tuanTask != null) {
                        tuanTask.setProgress(progress);
                        tuanMapper.updateById(tuanTask);
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新任务进度失败", e);
        }
    }

    /**
     * 计算任务进度百分比
     */
    private String calculateProgress(String taskId) {
        try {
            // 获取主任务信息
            NewSampling mainTask = samplingMapper.selectById(taskId);
            if (mainTask == null) {
                return "0";
            }

            // 根据任务层级计算进度
            String layerNum = mainTask.getLayerNum();
            if ("3".equals(layerNum)) {
                // 三层：兵团->师->团->连
                return calculateThreeLevelProgress(taskId);
            } else if ("2".equals(layerNum)) {
                // 两层：师->团->连
                return calculateTwoLevelProgress(taskId);
            } else if ("1".equals(layerNum)) {
                // 一层：团->连
                return calculateOneLevelProgress(taskId);
            }

            return "0";
        } catch (Exception e) {
            log.error("计算任务进度失败", e);
            return "0";
        }
    }

    /**
     * 计算兵团->师->团->连三层任务进度
     */
    private String calculateThreeLevelProgress(String btId) {
        // 获取师级任务列表
        List<ShiTask> shiTasks = shiMapper.selectByMainId(btId);
        if (shiTasks.isEmpty()) {
            return "0";
        }

        int totalShiTasks = shiTasks.size();
        int completedShiTasks = 0;

        for (ShiTask shiTask : shiTasks) {
            // 检查师级任务是否完成（有团级子任务）
            List<TuanTask> tuanTasks = tuanMapper.selectByMainId(shiTask.getId());
            if (!tuanTasks.isEmpty()) {
                completedShiTasks++;

                // 更新师级任务进度
                String shiProgress = calculateTuanProgress(shiTask.getId());
                shiTask.setProgress(shiProgress);
                shiMapper.updateById(shiTask);
            }
        }

        // 计算总体进度
        BigDecimal progress = BigDecimal.valueOf(completedShiTasks)
                .divide(BigDecimal.valueOf(totalShiTasks), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

        return progress.intValue() + "";
    }

    /**
     * 计算团级任务进度
     */
    private String calculateTuanProgress(String shiId) {
        List<TuanTask> tuanTasks = tuanMapper.selectByMainId(shiId);
        if (tuanTasks.isEmpty()) {
            return "0";
        }

        int totalTuanTasks = tuanTasks.size();
        int completedTuanTasks = 0;

        for (TuanTask tuanTask : tuanTasks) {
            // 检查团级任务是否完成（有连级子任务）
            List<LianTask> lianTasks = lianMapper.selectByMainId(tuanTask.getId());
            if (!lianTasks.isEmpty()) {
                completedTuanTasks++;

                // 更新团级任务进度
                String tuanProgress = calculateLianProgress(tuanTask.getId());
                tuanTask.setProgress(tuanProgress);
                tuanMapper.updateById(tuanTask);
            }
        }

        BigDecimal progress = BigDecimal.valueOf(completedTuanTasks)
                .divide(BigDecimal.valueOf(totalTuanTasks), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

        return progress.intValue() + "";
    }

    /**
     * 计算连级任务进度
     */
    private String calculateLianProgress(String tuanId) {
        List<LianTask> lianTasks = lianMapper.selectByMainId(tuanId);
        if (lianTasks.isEmpty()) {
            return "0";
        }

        int totalLianTasks = lianTasks.size();
        int completedLianTasks = 0;

        for (LianTask lianTask : lianTasks) {
            // 检查连级任务是否有实报人数
            if (StringUtils.hasText(lianTask.getRealNum()) &&
                Integer.parseInt(lianTask.getRealNum()) > 0) {
                completedLianTasks++;
            }
        }

        BigDecimal progress = BigDecimal.valueOf(completedLianTasks)
                .divide(BigDecimal.valueOf(totalLianTasks), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

        return progress.intValue() + "";
    }

    /**
     * 计算两层任务进度
     */
    private String calculateTwoLevelProgress(String taskId) {
        // 师级作为主任务，计算团级进度
        return calculateTuanProgress(taskId);
    }

    /**
     * 计算一层任务进度
     */
    private String calculateOneLevelProgress(String taskId) {
        // 团级作为主任务，计算连级进度
        return calculateLianProgress(taskId);
    }

    /**
     * 构建上报数据查询条件
     */
    private QueryWrapper<SamplingTaskReport> buildQueryWrapper(SampleDto dto) {
        QueryWrapper<SamplingTaskReport> queryWrapper = new QueryWrapper<>();

        // 根据任务ID查询
        if (dto.getBtId() != null && !dto.getBtId().isEmpty()) {
            queryWrapper.eq("task_id", dto.getBtId());
            log.debug("添加查询条件: task_id = {}", dto.getBtId());
        }

        // 根据年份查询
        if (dto.getYears() != null && !dto.getYears().isEmpty()) {
            queryWrapper.eq("years", dto.getYears());
            log.debug("添加查询条件: years = {}", dto.getYears());
        }

        return queryWrapper;
    }

    /**
     * 调试空数据情况
     */
    private void handleEmptyDataForDebug(SampleDto dto) {
        log.warn("未查询到数据 - 任务ID: {}, 年份: {}", dto.getBtId(), dto.getYears());

        // 尝试查询所有数据进行调试
        try {
            QueryWrapper<SamplingTaskReport> debugWrapper = new QueryWrapper<>();
            List<SamplingTaskReport> allData = samplingTaskReportService.list(debugWrapper);
            log.info("数据库诊断 - 总记录数: {}", allData != null ? allData.size() : 0);

            if (allData != null && !allData.isEmpty()) {
                // 记录前几条数据的关键字段用于调试
                for (int i = 0; i < Math.min(3, allData.size()); i++) {
                    SamplingTaskReport report = allData.get(i);
                    log.debug("样本数据{} - 任务ID: {}, 状态: {}, 地区: {}",
                            (i+1), report.getTaskId(), report.getReportStatus(), report.getZoneName());
                }
            }
        } catch (Exception e) {
            log.error("数据库诊断失败", e);
        }
    }

    /**
     * 转换上报数据为Popular格式
     */
    private List<Popular> convertToPopularList(List<SamplingTaskReport> reportList) {
        List<Popular> list = new ArrayList<>();

        for (SamplingTaskReport report : reportList) {
            try {
                Popular popular = new Popular();
                popular.setId(report.getId());
                popular.setTaskId(report.getTaskId());
                popular.setYears(report.getYears());
                popular.setName(report.getZoneName());
                popular.setCode(report.getZoneCode());
                popular.setFullName(report.getZoneName());
                popular.setPopular(report.getResidentPopulation());

                list.add(popular);
            } catch (Exception e) {
                log.error("转换Popular对象失败 - 记录ID: {}, 错误: {}", report.getId(), e.getMessage());
            }
        }

        return list;
    }

    /**
     * 执行抽样（如果需要）
     */
    private List<Popular> performSamplingIfNeeded(List<Popular> popularList, SampleDto dto) {
        // 如果没有指定抽样参数，返回全部数据
        if (dto.getSampleRandom() == null || dto.getSampleRandom().isEmpty()) {
            log.info("未指定随机因子，返回全部数据 - 数据量: {}", popularList.size());
            return popularList;
        }

        try {
            int sampleRandom = Integer.parseInt(dto.getSampleRandom());
            // 根据任务配置获取抽样数量，而不是使用用户输入的固定值
            int sampleCount = getSampleCountFromTaskConfig(dto);

            log.info("开始执行抽样 - 源数据量: {}, 抽样数量: {}, 随机种子: {}",
                    popularList.size(), sampleCount, sampleRandom);

            // 执行抽样算法
            List<Popular> sampledResult = performRandomSampling(popularList, sampleCount, sampleRandom);

            log.info("抽样完成 - 返回{}条数据", sampledResult.size());
            return sampledResult;

        } catch (NumberFormatException e) {
            log.error("抽样参数格式错误 - 随机因子: {}, 抽样数量: {}",
                    dto.getSampleRandom(), dto.getSampleNum());
            return popularList; // 参数错误时返回原始列表
        }
    }

    /**
     * 根据任务配置获取抽样数量
     */
    private int getSampleCountFromTaskConfig(SampleDto dto) {
        try {
            // 根据任务级别和层级确定抽样数量
            String taskLevel = dto.getTaskLevel();
            String layerNum = dto.getLayerNum();

            // 如果DTO中已经有配置的抽样数量，直接使用
            if (taskLevel != null && layerNum != null) {
                return getSampleCountByLevel(dto, taskLevel, layerNum);
            }

            // 如果DTO中没有配置，尝试从数据库查询任务配置
            String taskId = dto.getTaskIdByLevel();
            if (taskId != null) {
                NewSampling taskConfig = this.getById(taskId);
                if (taskConfig != null) {
                    return getSampleCountByLevel(taskConfig, dto.getTaskLevel());
                }
            }

            // 如果都没有，使用用户输入的数量或默认值
            return parseSampleCount(dto.getSampleNum());

        } catch (Exception e) {
            log.warn("获取任务配置抽样数量失败，使用默认值: {}", e.getMessage());
            return parseSampleCount(dto.getSampleNum());
        }
    }

    /**
     * 根据任务级别和层级从DTO获取抽样数量
     */
    private int getSampleCountByLevel(SampleDto dto, String taskLevel, String layerNum) {
        // 根据层级和任务级别确定抽样数量
        if ("3".equals(layerNum)) { // 三层：兵团->师->团->连
            switch (taskLevel) {
                case "1": return parseIntSafely(dto.getShiNum(), 3);      // 兵团级任务，抽取师级数量
                case "2": return parseIntSafely(dto.getTuanNum(), 3);     // 师级任务，抽取团级数量
                case "3": return parseIntSafely(dto.getLianNum(), 3);     // 团级任务，抽取连级数量
                case "4": return parseIntSafely(dto.getSampleNum(), 3);   // 连级任务，使用需报人数
            }
        } else if ("2".equals(layerNum)) { // 两层：师->团->连
            switch (taskLevel) {
                case "2": return parseIntSafely(dto.getTuanNum(), 3);     // 师级任务，抽取团级数量
                case "3": return parseIntSafely(dto.getLianNum(), 3);     // 团级任务，抽取连级数量
                case "4": return parseIntSafely(dto.getSampleNum(), 3);   // 连级任务，使用需报人数
            }
        } else if ("1".equals(layerNum)) { // 一层：团->连
            switch (taskLevel) {
                case "3": return parseIntSafely(dto.getLianNum(), 3);     // 团级任务，抽取连级数量
                case "4": return parseIntSafely(dto.getSampleNum(), 3);   // 连级任务，使用需报人数
            }
        }

        return 3; // 默认值
    }

    /**
     * 根据任务级别从任务配置获取抽样数量
     */
    private int getSampleCountByLevel(NewSampling taskConfig, String taskLevel) {
        if (taskConfig == null || taskLevel == null) {
            return 3;
        }

        String layerNum = taskConfig.getLayerNum();

        if ("3".equals(layerNum)) { // 三层：兵团->师->团->连
            switch (taskLevel) {
                case "1": return parseIntSafely(taskConfig.getShiNum(), 3);      // 兵团级任务，抽取师级数量
                case "2": return parseIntSafely(taskConfig.getTuanNum(), 3);     // 师级任务，抽取团级数量
                case "3": return parseIntSafely(taskConfig.getLianNum(), 3);     // 团级任务，抽取连级数量
                case "4": return parseIntSafely(taskConfig.getTotalNum(), 3);    // 连级任务，使用总计人数
            }
        } else if ("2".equals(layerNum)) { // 两层：师->团->连
            switch (taskLevel) {
                case "2": return parseIntSafely(taskConfig.getTuanNum(), 3);     // 师级任务，抽取团级数量
                case "3": return parseIntSafely(taskConfig.getLianNum(), 3);     // 团级任务，抽取连级数量
                case "4": return parseIntSafely(taskConfig.getTotalNum(), 3);    // 连级任务，使用总计人数
            }
        } else if ("1".equals(layerNum)) { // 一层：团->连
            switch (taskLevel) {
                case "3": return parseIntSafely(taskConfig.getLianNum(), 3);     // 团级任务，抽取连级数量
                case "4": return parseIntSafely(taskConfig.getTotalNum(), 3);    // 连级任务，使用总计人数
            }
        }

        return 3; // 默认值
    }

    /**
     * 安全地解析整数，失败时返回默认值
     */
    private int parseIntSafely(String value, int defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            int result = Integer.parseInt(value.trim());
            return result > 0 ? result : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("解析数字失败: {}, 使用默认值: {}", value, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 解析抽样数量（保留原方法作为兜底）
     */
    private int parseSampleCount(String sampleNumStr) {
        if (sampleNumStr == null || sampleNumStr.isEmpty()) {
            log.debug("抽样数量参数为空，使用默认值3");
            return 3; // 默认值
        }

        try {
            int sampleCount = Integer.parseInt(sampleNumStr);
            if (sampleCount <= 0) {
                log.warn("抽样数量无效: {}, 使用默认值3", sampleCount);
                return 3;
            }
            return sampleCount;
        } catch (NumberFormatException e) {
            log.warn("无法解析抽样数量: {}, 使用默认值3", sampleNumStr);
            return 3;
        }
    }

    /**
     * 执行随机抽样算法
     */
    private List<Popular> performRandomSampling(List<Popular> sourceList, int sampleCount, int randomSeed) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        // 确保抽样数量不超过源数据量
        int actualSampleCount = Math.min(sampleCount, sourceList.size());

        // 使用固定的随机种子确保结果可重现
        long seed = randomSeed * 31L + sourceList.hashCode();
        Random random = new Random(seed);

        // 执行随机抽样
        List<Popular> result = random.ints(0, sourceList.size())
                .distinct()
                .limit(actualSampleCount)
                .mapToObj(sourceList::get)
                .collect(Collectors.toList());

        log.debug("随机抽样执行完成 - 种子: " + seed + ", 源数据量: " + sourceList.size() +
                ", 抽样数量: " + sampleCount + ", 实际抽样: " + result.size());

        return result;
    }

    /**
     * 验证兵团抽样参数
     */
    private void validateBtSampleParams(SampleDto dto) {
        if (dto.getBtId() == null || dto.getBtId().isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        if (dto.getZoneIds() == null || dto.getZoneIds().isEmpty()) {
            throw new IllegalArgumentException("地区IDs不能为空");
        }
        if (dto.getShiSampleNum() == null || dto.getShiSampleNum().isEmpty()) {
            throw new IllegalArgumentException("师级抽样数量不能为空");
        }

        try {
            Integer.parseInt(dto.getShiSampleNum());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("师级抽样数量格式错误");
        }
    }

    /**
     * 根据地区IDs获取地区信息
     */
    private List<ZoneVo> getZoneListByIds(String zoneIds) {
        // 这里需要根据实际的查询逻辑来实现
        // 暂时返回空列表，需要根据具体的Mapper方法来实现
        return new ArrayList<>();
    }

    /**
     * 创建师级任务列表
     */
    private List<ShiTask> createShiTaskList(List<ZoneVo> voList, SampleDto dto) {
        List<ShiTask> shiTaskList = new ArrayList<>();

        for (ZoneVo vo : voList) {
            ShiTask shiTask = new ShiTask();
            shiTask.setId(CdsUtils.createCdsId(vo.getCode()));
            shiTask.setBtId(dto.getBtId());
            shiTask.setSampleNum(dto.getShiSampleNum());
            shiTask.setSampleZone(vo.getCode());
            shiTask.setCreateTime(new Date());
            shiTask.setYears(dto.getYears());
            shiTask.setTaskStatus("1");
            shiTask.setTaskLevel("2");
            shiTask.setName(dto.getName());

            shiTaskList.add(shiTask);
        }

        return shiTaskList;
    }

    /**
     * 安全地推送抽样结果
     */
    private void pushSamplingResultsSafely(String taskId, String level) {
        try {
            pushService.pushSamplingResults(taskId, level);
            log.info("抽样结果推送成功 - 任务ID: {}, 层级: {}", taskId, level);
        } catch (Exception e) {
            log.error("抽样结果推送失败 - 任务ID: " + taskId + ", 层级: " + level + ", 错误: " + e.getMessage());
            log.error("推送异常详情", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 验证师级抽样参数
     */
    private void validateShiSampleParams(SampleDto dto) {
        if (dto.getShiId() == null || dto.getShiId().isEmpty()) {
            throw new IllegalArgumentException("师级ID不能为空");
        }
        if (dto.getZoneIds() == null || dto.getZoneIds().isEmpty()) {
            throw new IllegalArgumentException("地区IDs不能为空");
        }
        if (dto.getTuanSampleNum() == null || dto.getTuanSampleNum().isEmpty()) {
            throw new IllegalArgumentException("团级抽样数量不能为空");
        }

        try {
            Integer.parseInt(dto.getTuanSampleNum());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("团级抽样数量格式错误");
        }
    }

    /**
     * 创建团级任务列表
     */
    private List<TuanTask> createTuanTaskList(SampleDto dto) {
        List<TuanTask> tuanTaskList = new ArrayList<>();
        String[] zoneIds = dto.getZoneIds().split(",");

        for (String zoneId : zoneIds) {
            TuanTask tuanTask = new TuanTask();
            tuanTask.setId(CdsUtils.createCdsId(zoneId));
            tuanTask.setShiId(dto.getShiId());
            tuanTask.setBtId(dto.getBtId());
            tuanTask.setSampleNum(dto.getTuanSampleNum());
            tuanTask.setSampleZone(zoneId);
            tuanTask.setCreateTime(new Date());
            tuanTask.setYears(dto.getYears());
            tuanTask.setStatus("1");
            tuanTask.setTaskLevel("3");
            tuanTask.setName(dto.getName());

            tuanTaskList.add(tuanTask);
        }

        return tuanTaskList;
    }

    /**
     * 验证团级抽样参数
     */
    private void validateTuanSampleParams(SampleDto dto) {
        if (dto.getTuanId() == null || dto.getTuanId().isEmpty()) {
            throw new IllegalArgumentException("团级ID不能为空");
        }
        if (dto.getZoneIds() == null || dto.getZoneIds().isEmpty()) {
            throw new IllegalArgumentException("地区IDs不能为空");
        }
        if (dto.getLianSampleNum() == null || dto.getLianSampleNum().isEmpty()) {
            throw new IllegalArgumentException("连级抽样数量不能为空");
        }

        try {
            Integer.parseInt(dto.getLianSampleNum());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("连级抽样数量格式错误");
        }
    }

    /**
     * 创建连级任务列表
     */
    private List<LianTask> createLianTaskList(SampleDto dto) {
        List<LianTask> lianTaskList = new ArrayList<>();
        String[] zoneIds = dto.getZoneIds().split(",");

        for (String zoneId : zoneIds) {
            LianTask lianTask = new LianTask();
            lianTask.setId(CdsUtils.createCdsId(zoneId));
            lianTask.setTuanId(dto.getTuanId());
            lianTask.setShiId(dto.getShiId());
            lianTask.setBtId(dto.getBtId());
            lianTask.setSampleNum(dto.getLianSampleNum());
            lianTask.setSampleZone(zoneId);
            lianTask.setCreateTime(new Date());
            lianTask.setYears(dto.getYears());
            lianTask.setStatus("1");
            lianTask.setTaskLevel("4");
            lianTask.setName(dto.getName());

            lianTaskList.add(lianTask);
        }

        return lianTaskList;
    }

    /**
     * 创建新的抽样任务（包含业务逻辑处理）
     */
    @Override
    @Transactional
    public String createSamplingTask(NewSampling newSampling, LoginUser user) {
        try {
            // 设置用户相关信息
            newSampling.setOrgcode(user.getOrgcode());
            newSampling.setZonecode(user.getZonecode());
            newSampling.setProgress("0");
            newSampling.setStatus("1"); // 待确认

            // 根据层数设置任务级别
            if ("3".equals(newSampling.getLayerNum())) {
                // 3层对应兵团任务
                newSampling.setTaskLevel("1"); // 任务级别
            } else if ("2".equals(newSampling.getLayerNum())) {
                // 2层对应师级任务
                newSampling.setTaskLevel("2"); // 任务级别
            } else if ("1".equals(newSampling.getLayerNum())) {
                // 1层对应团场任务
                newSampling.setTaskLevel("3"); // 任务级别
            }

            // 保存主任务
            this.save(newSampling);
            log.info("创建主任务成功，任务ID: {}, 层数: {}, 任务级别: {}",
                    newSampling.getId(), newSampling.getLayerNum(), newSampling.getTaskLevel());

            // 根据层数自动创建各级任务
            createTasksByLayer(newSampling, user);

            return "添加成功！";
        } catch (Exception e) {
            log.error("创建抽样任务失败", e);
            throw new RuntimeException("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据层数自动创建各级任务
     */
    private void createTasksByLayer(NewSampling mainTask, LoginUser user) {
        try {
            String layerNum = mainTask.getLayerNum();
            log.info("开始根据层数创建各级任务 - 任务ID: {}, 层数: {}", mainTask.getId(), layerNum);

            if ("3".equals(layerNum)) {
                // 3层抽样：兵团 -> 师 -> 团 -> 连
                createInitialShiTasks(mainTask, user);
                createInitialTuanTasks(mainTask, user);
                createInitialLianTasks(mainTask, user);
                log.info("3层抽样任务创建完成 - 已创建师级、团级、连级任务");
            } else if ("2".equals(layerNum)) {
                // 2层抽样：师 -> 团 -> 连
                createInitialTuanTasks(mainTask, user);
                createInitialLianTasks(mainTask, user);
                log.info("2层抽样任务创建完成 - 已创建团级、连级任务");
            } else if ("1".equals(layerNum)) {
                // 1层抽样：团 -> 连
                createInitialLianTasks(mainTask, user);
                log.info("1层抽样任务创建完成 - 已创建连级任务");
            }
        } catch (Exception e) {
            log.error("根据层数创建各级任务失败 - 任务ID: {}", mainTask.getId(), e);
            throw new RuntimeException("创建各级任务失败: " + e.getMessage());
        }
    }

    /**
     * 数据上报业务处理
     */
    @Override
    @Transactional
    public String reportDataBusiness(SamplingTaskReport samplingTaskReport, LoginUser user) {
        try {
            // 处理年份字段，确保只保存年份部分（最多4位）
            if (samplingTaskReport.getYears() != null && samplingTaskReport.getYears().length() > 4) {
                String years = samplingTaskReport.getYears();
                // 如果是日期格式，提取年份
                if (years.contains("-") || years.contains("/")) {
                    try {
                        java.util.Date date = new java.text.SimpleDateFormat("yyyy-MM-dd").parse(years.substring(0, 10));
                        java.util.Calendar cal = java.util.Calendar.getInstance();
                        cal.setTime(date);
                        samplingTaskReport.setYears(String.valueOf(cal.get(java.util.Calendar.YEAR)));
                    } catch (Exception e) {
                        // 如果解析失败，直接截取前4位
                        samplingTaskReport.setYears(years.substring(0, 4));
                    }
                } else {
                    // 如果不是日期格式，直接截取前4位
                    samplingTaskReport.setYears(years.substring(0, 4));
                }
            }

            if (samplingTaskReport.getId() == null || samplingTaskReport.getId().isEmpty()) {
                // 新增 - 强制设置为已上报状态，确保点击确定后直接为已上报
                samplingTaskReport.setCreateBy(user.getRealname());
                samplingTaskReport.setCreateTime(new java.util.Date());
                samplingTaskReport.setReportOrg(user.getOrgcode());
                samplingTaskReport.setReportStatus("2"); // 强制设置为已上报状态，无论前端传什么值
                samplingTaskReportService.save(samplingTaskReport);

                // 更新任务状态为待抽取
                taskStatusService.updateToWaitingSampling(samplingTaskReport.getTaskId());

                log.info("数据上报成功，任务ID: {}, 上报人: {}", samplingTaskReport.getTaskId(), user.getRealname());
            } else {
                // 编辑
                samplingTaskReport.setUpdateBy(user.getRealname());
                samplingTaskReport.setUpdateTime(new java.util.Date());
                samplingTaskReportService.updateById(samplingTaskReport);

                log.info("数据更新成功，任务ID: {}, 更新人: {}", samplingTaskReport.getTaskId(), user.getRealname());
            }

            return "保存成功";
        } catch (Exception e) {
            log.error("数据上报业务处理失败", e);
            throw new RuntimeException("保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取上报数据列表（带分页）
     */
    @Override
    public IPage<SamplingReportVO> getReportListWithPagination(String taskId, Integer pageNo, Integer pageSize) {
        try {
            // 使用SampleDto来调用现有的queryZoneReportList方法
            SampleDto dto = new SampleDto();
            dto.setBtId(taskId);

            // 获取处理后的VO数据
            List<SamplingReportVO> voList = this.queryZoneReportList(dto);

            // 手动分页
            Page<SamplingReportVO> page = new Page<>(pageNo, pageSize);
            int total = voList != null ? voList.size() : 0;
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, total);

            List<SamplingReportVO> pageData = new ArrayList<>();
            if (start < total) {
                pageData = voList.subList(start, end);
            }

            page.setTotal(total);
            page.setRecords(pageData);

            log.info("获取上报数据列表成功，任务ID: {}, 总数: {}, 当前页: {}", taskId, total, pageNo);
            return page;
        } catch (Exception e) {
            log.error("获取上报数据列表失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 生成数据上报模板
     */
    @Override
    public ModelAndView generateReportTemplate(boolean isTemplate) {
        try {
            String fileName = "数据上报模板";

            if (isTemplate) {
                // 生成新的模板格式：地区名称、地区编码、街道名称、街道编码、常住人口数
                List<org.jeecg.modules.newCds.newSampling.entity.ReportDataTemplate> exportList = new ArrayList<>();

                // AutoPoi 导出Excel
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, fileName);
                mv.addObject(NormalExcelConstants.CLASS, org.jeecg.modules.newCds.newSampling.entity.ReportDataTemplate.class);
                ExportParams exportParams = new ExportParams(fileName, fileName);
                mv.addObject(NormalExcelConstants.PARAMS, exportParams);
                mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

                log.info("生成新格式数据上报模板成功");
                return mv;
            } else {
                // 原有的模板下载逻辑
                List<Popular> exportList = new ArrayList<>();

                // AutoPoi 导出Excel
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(NormalExcelConstants.FILE_NAME, "师级地区人口数批量上报模板");
                mv.addObject(NormalExcelConstants.CLASS, Popular.class);
                ExportParams exportParams = new ExportParams("师级地区人口数批量上报模板", "师级地区人口数批量上报模板");
                mv.addObject(NormalExcelConstants.PARAMS, exportParams);
                mv.addObject(NormalExcelConstants.DATA_LIST, exportList);

                log.info("生成原格式数据上报模板成功");
                return mv;
            }
        } catch (Exception e) {
            log.error("生成数据上报模板失败", e);
            throw new RuntimeException("生成模板失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户级别查询对应的任务列表
     */
    @Override
    public IPage<?> queryTasksByUserLevel(LoginUser user, Integer pageNo, Integer pageSize, java.util.Map<String, String[]> queryParams) {
        try {
            if (user == null || user.getZonecode() == null) {
                throw new RuntimeException("用户信息获取失败，无法查询任务");
            }

            // 获取用户级别
            int userLevel = getUserZoneLevel(user.getZonecode());
            log.info("用户级别查询 - 用户: {}, 地区编码: {}, 级别: {}", user.getRealname(), user.getZonecode(), userLevel);

            Page<?> page = new Page<>(pageNo, pageSize);

            switch (userLevel) {
                case 1: // 兵团级 - 查询主任务表
                    return queryMainTasks(user, page, queryParams);
                case 2: // 师级 - 查询师级任务表
                    return queryShiTasks(user, page, queryParams);
                case 3: // 团级 - 查询团级任务表
                    return queryTuanTasks(user, page, queryParams);
                case 4: // 连级 - 查询连级任务表
                    return queryLianTasks(user, page, queryParams);
                default:
                    log.warn("未知的用户级别: {}, 默认查询主任务表", userLevel);
                    return queryMainTasks(user, page, queryParams);
            }
        } catch (Exception e) {
            log.error("根据用户级别查询任务失败", e);
            throw new RuntimeException("查询任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询主任务表（兵团级）
     */
    private IPage<NewSampling> queryMainTasks(LoginUser user, Page<?> page, java.util.Map<String, String[]> queryParams) {
        try {
            NewSampling queryEntity = new NewSampling();
            QueryWrapper<NewSampling> queryWrapper = QueryGenerator.initQueryWrapper(queryEntity, queryParams);

            // 添加地区权限过滤
            queryWrapper.likeRight("zonecode", user.getZonecode());
            queryWrapper.orderByDesc("create_time");

            Page<NewSampling> mainPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<NewSampling> result = this.page(mainPage, queryWrapper);

            log.info("兵团级任务查询完成 - 总数: {}", result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("查询主任务表失败", e);
            throw new RuntimeException("查询兵团级任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询师级任务表
     */
    private IPage<ShiTask> queryShiTasks(LoginUser user, Page<?> page, java.util.Map<String, String[]> queryParams) {
        try {
            QueryWrapper<ShiTask> queryWrapper = new QueryWrapper<>();

            // 师级用户只能看到自己地区的师级任务
            queryWrapper.likeRight("sample_zone", user.getZonecode());
            queryWrapper.orderByDesc("create_time");

            Page<ShiTask> shiPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<ShiTask> result = shiService.page(shiPage, queryWrapper);

            log.info("师级任务查询完成 - 总数: {}", result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("查询师级任务表失败", e);
            throw new RuntimeException("查询师级任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询团级任务表
     */
    private IPage<TuanTask> queryTuanTasks(LoginUser user, Page<?> page, java.util.Map<String, String[]> queryParams) {
        try {
            QueryWrapper<TuanTask> queryWrapper = new QueryWrapper<>();

            // 团级用户只能看到自己地区的团级任务
            queryWrapper.likeRight("sample_zone", user.getZonecode());
            queryWrapper.orderByDesc("create_time");

            Page<TuanTask> tuanPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<TuanTask> result = tuanService.page(tuanPage, queryWrapper);

            log.info("团级任务查询完成 - 总数: {}", result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("查询团级任务表失败", e);
            throw new RuntimeException("查询团级任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询连级任务表
     */
    private IPage<LianTask> queryLianTasks(LoginUser user, Page<?> page, java.util.Map<String, String[]> queryParams) {
        try {
            QueryWrapper<LianTask> queryWrapper = new QueryWrapper<>();

            // 连级用户只能看到自己地区的连级任务
            queryWrapper.likeRight("sample_zone", user.getZonecode());
            queryWrapper.orderByDesc("create_time");

            Page<LianTask> lianPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<LianTask> result = lianService.page(lianPage, queryWrapper);

            log.info("连级任务查询完成 - 总数: {}", result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("查询连级任务表失败", e);
            throw new RuntimeException("查询连级任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户地区级别
     */
    private int getUserZoneLevel(String zonecode) {
        if (zonecode == null || zonecode.length() < 9) {
            return 4; // 默认连级
        }

        // 根据地区编码规则判断级别
        String levelPart = zonecode.substring(6, 9);
        if ("000".equals(levelPart)) {
            return 1; // 兵团级：xxxxxx000
        } else if (zonecode.endsWith("000")) {
            return 2; // 师级：xxxxxxx00
        } else if (zonecode.endsWith("00")) {
            return 3; // 团级：xxxxxxxx0
        } else {
            return 4; // 连级：xxxxxxxxx
        }
    }

    /**
     * 创建初始师级任务
     */
    private void createInitialShiTasks(NewSampling mainTask, LoginUser user) {
        try {
            log.info("开始创建师级任务，主任务ID: {}", mainTask.getId());

            // 创建一个默认的师级任务，根据数据库表结构设置必需字段
            ShiTask shiTask = new ShiTask();
            String shiTaskId = CdsUtils.createCdsId("660000000");
            shiTask.setId(shiTaskId);
            shiTask.setBtId(mainTask.getId());
            shiTask.setName(mainTask.getName()); // name字段是必需的
            shiTask.setYears(mainTask.getYears());
            shiTask.setTaskStatus("1"); // task_status字段：待确认
            shiTask.setSampleZone("660000000"); // sample_zone字段
            shiTask.setCreateTime(new java.util.Date());

            log.info("师级任务对象创建完成，准备保存到数据库: ID={}, BtId={}, Name={}, Years={}",
                    shiTaskId, mainTask.getId(), mainTask.getName(), mainTask.getYears());

            shiService.save(shiTask);
            log.info("创建初始师级任务成功 - 任务ID: {}", shiTaskId);
        } catch (Exception e) {
            log.error("创建初始师级任务失败，详细错误信息: ", e);
            throw new RuntimeException("创建师级任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建初始团级任务
     */
    private void createInitialTuanTasks(NewSampling mainTask, LoginUser user) {
        try {
            log.info("开始创建团级任务，主任务ID: {}", mainTask.getId());

            // 创建一个默认的团级任务，根据数据库表结构设置必需字段
            TuanTask tuanTask = new TuanTask();
            String tuanTaskId = CdsUtils.createCdsId("660000000");
            tuanTask.setId(tuanTaskId);
            tuanTask.setBtId(mainTask.getId());
            tuanTask.setName(mainTask.getName()); // name字段是必需的
            tuanTask.setYears(mainTask.getYears());
            tuanTask.setStatus("1"); // status字段：待确认
            tuanTask.setSampleZone("660000000"); // sample_zone字段
            tuanTask.setCreateTime(new java.util.Date());

            log.info("团级任务对象创建完成，准备保存到数据库: ID={}, BtId={}", tuanTaskId, mainTask.getId());

            tuanService.save(tuanTask);
            log.info("创建初始团级任务成功 - 任务ID: {}", tuanTaskId);
        } catch (Exception e) {
            log.error("创建初始团级任务失败，详细错误信息: ", e);
            throw new RuntimeException("创建团级任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建初始连级任务
     */
    private void createInitialLianTasks(NewSampling mainTask, LoginUser user) {
        try {
            // 创建一个默认的连级任务，根据数据库表结构设置必需字段
            LianTask lianTask = new LianTask();
            lianTask.setId(CdsUtils.createCdsId("660000000")); // 使用固定值
            lianTask.setBtId(mainTask.getId());
            lianTask.setName(mainTask.getName()); // name字段是必需的
            lianTask.setYears(mainTask.getYears());
            lianTask.setStatus("1"); // status字段：待确认
            lianTask.setSampleZone("660000000"); // sample_zone字段
            lianTask.setCreateTime(new java.util.Date());

            lianService.save(lianTask);
            log.info("创建初始连级任务成功 - 任务ID: {}", lianTask.getId());
        } catch (Exception e) {
            log.error("创建初始连级任务失败", e);
            throw new RuntimeException("创建连级任务失败: " + e.getMessage());
        }
    }

}
